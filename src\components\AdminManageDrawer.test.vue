<template>
  <div class="test-container">
    <h2>AdminManageDrawer 响应式测试</h2>
    <div class="screen-info">
      <p>当前屏幕宽度: {{ screenWidth }}px</p>
      <p>抽屉尺寸: {{ drawerSize }}</p>
      <p>对话框宽度: {{ dialogWidth }}</p>
    </div>
    
    <div class="test-buttons">
      <el-button type="primary" @click="showDrawer">显示管理员抽屉</el-button>
      <el-button @click="simulateResize(768)">模拟移动端 (768px)</el-button>
      <el-button @click="simulateResize(1024)">模拟平板 (1024px)</el-button>
      <el-button @click="simulateResize(1440)">模拟小桌面 (1440px)</el-button>
      <el-button @click="simulateResize(1920)">模拟大桌面 (1920px)</el-button>
    </div>
    
    <AdminManageDrawer 
      :visible.sync="drawerVisible"
      @create-admin="handleCreateAdmin"
    />
  </div>
</template>

<script>
import AdminManageDrawer from './AdminManageDrawer.vue'

export default {
  name: 'AdminManageDrawerTest',
  components: {
    AdminManageDrawer
  },
  data() {
    return {
      drawerVisible: false,
      screenWidth: window.innerWidth
    }
  },
  computed: {
    drawerSize() {
      if (this.screenWidth <= 768) {
        return '90%'
      } else if (this.screenWidth <= 1024) {
        return '70%'
      } else if (this.screenWidth <= 1440) {
        return '50%'
      } else {
        return '40%'
      }
    },
    dialogWidth() {
      if (this.screenWidth <= 768) {
        return '95%'
      } else if (this.screenWidth <= 1024) {
        return '80%'
      } else if (this.screenWidth <= 1440) {
        return '60%'
      } else {
        return '40%'
      }
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    handleResize() {
      this.screenWidth = window.innerWidth
    },
    showDrawer() {
      this.drawerVisible = true
    },
    simulateResize(width) {
      this.screenWidth = width
      this.$message.info(`模拟屏幕宽度: ${width}px`)
    },
    handleCreateAdmin() {
      this.$message.success('创建管理员功能被触发')
    }
  }
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.screen-info {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  margin: 20px 0;
}

.screen-info p {
  margin: 5px 0;
  font-weight: bold;
}

.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin: 20px 0;
}

.test-buttons .el-button {
  margin: 0;
}

@media (max-width: 768px) {
  .test-buttons {
    flex-direction: column;
  }
  
  .test-buttons .el-button {
    width: 100%;
  }
}
</style>
